<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\KenanTepeIdExport;

class ExportKenanTepeIds extends Command
{
    protected $signature = 'export:kenan-tepe-ids';
    protected $description = 'Fetches Kenan Tepe records and exports unique IDs to Excel';

    public function handle()
    {
        $url = 'https://opencontext.org/query/Asia/Turkey/Kenan+Tepe.json';

        $this->info("Fetching data from $url ...");
        $response = Http::get($url);

        if (!$response->ok()) {
            $this->error('Failed to fetch data.');
            return;
        }

        $data = $response->json();

        // Traverse and collect all "id"
        $ids = $this->extractIds($data);

        // De-duplicate
        $uniqueIds = array_unique($ids);

        // Export to Excel
        $filename = 'kenan_tepe_ids.xlsx';
        Excel::store(new KenanTepeIdExport($uniqueIds), $filename);

        $this->info("Exported " . count($uniqueIds) . " unique IDs to $filename");
    }

    private function extractIds($data)
    {
        $ids = [];

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveArrayIterator($data)
        );

        foreach ($iterator as $key => $value) {
            if ($key === 'id') {
                $ids[] = $value;
            }
        }

        return $ids;
    }
}